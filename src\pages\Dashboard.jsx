import { useState, useEffect } from 'react'
import { tradesService } from '../services/tradesService'
import { bhavcopyService } from '../services/bhavcopyService'

const Dashboard = () => {
  const [summary, setSummary] = useState({
    totalTrades: 0,
    openPositions: 0,
    realizedPnL: 0,
    unrealizedPnL: 0,
    totalValue: 0,
    winRate: 0
  })
  const [portfolioHistory, setPortfolioHistory] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    loadSummary()
    loadPortfolioHistory()
  }, [])

  const loadSummary = async () => {
    try {
      const data = await tradesService.getPortfolioSummary()
      setSummary(data)
    } catch (err) {
      setError('Failed to load portfolio summary')
      console.error(err)
    }
  }

  const loadPortfolioHistory = async () => {
    try {
      // For now, we'll create a mock portfolio history
      const history = []
      const now = new Date()
      let value = 100000 // Starting value
      
      for (let i = 30; i >= 0; i--) {
        const date = new Date(now)
        date.setDate(date.getDate() - i)
        
        // Create some variation in portfolio value
        const variation = (Math.random() - 0.5) * 0.02 // +/- 1%
        value = value * (1 + variation)
        
        history.push({
          date: date.toISOString().split('T')[0],
          value: value
        })
      }
      
      setPortfolioHistory(history)
    } catch (err) {
      console.error('Failed to load portfolio history', err)
    }
  }

  const refreshData = async () => {
    try {
      setLoading(true)
      await loadSummary()
      await loadPortfolioHistory()
    } catch (err) {
      setError('Failed to refresh data')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  if (loading && !summary.totalTrades) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center">
          <p className="text-red-800">{error}</p>
          <button
            onClick={refreshData}
            className="text-sm text-red-600 hover:text-red-800"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Dashboard</h2>
        <button
          onClick={refreshData}
          disabled={loading}
          className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          {loading ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Portfolio Value</h3>
          <p className="text-3xl font-bold text-gray-900">₹{summary.totalValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</p>
          <p className={`text-sm mt-1 ${summary.unrealizedPnL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {summary.unrealizedPnL >= 0 ? '+' : ''}{summary.unrealizedPnL.toFixed(2)}% today
          </p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Total Trades</h3>
          <p className="text-3xl font-bold text-gray-900">{summary.totalTrades}</p>
          <p className="text-sm text-gray-500 mt-1">{summary.openPositions} open positions</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Win Rate</h3>
          <p className="text-3xl font-bold text-gray-900">{summary.winRate.toFixed(1)}%</p>
          <p className="text-sm text-gray-500 mt-1">{summary.realizedPnL >= 0 ? 'Profitable' : 'Unprofitable'} trades</p>
        </div>
      </div>

      {/* Equity Curve Chart */}
      {portfolioHistory.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Equity Curve</h3>
          <div className="h-64 flex items-end space-x-1">
            {portfolioHistory.map((point, index) => {
              // Normalize heights to fit in the chart
              const minValue = Math.min(...portfolioHistory.map(p => p.value))
              const maxValue = Math.max(...portfolioHistory.map(p => p.value))
              const height = ((point.value - minValue) / (maxValue - minValue)) * 200
              
              return (
                <div
                  key={index}
                  className="flex-1 bg-indigo-600 rounded-t"
                  style={{ height: `${height}px` }}
                  title={`${point.date}: ₹${point.value.toLocaleString(undefined, { minimumFractionDigits: 2 })}`}
                ></div>
              )
            })}
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-2">
            <span>{portfolioHistory[0].date}</span>
            <span>{portfolioHistory[portfolioHistory.length - 1].date}</span>
          </div>
        </div>
      )}

      {/* Recent Trades */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Trades</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Symbol
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Qty
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {summary.totalTrades === 0 ? (
                <tr>
                  <td colSpan="5" className="px-6 py-4 text-sm text-gray-500 text-center">
                    No trades yet. Add your first trade to get started.
                  </td>
                </tr>
              ) : (
                // For now, we'll show a mock list of recent trades
                Array.from({ length: Math.min(summary.totalTrades, 5) }).map((_, i) => (
                  <tr key={i}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      RELIANCE
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                        BUY
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      10
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ₹2,850.00
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date().toLocaleDateString()}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default Dashboard