import { useState, useEffect } from 'react'
import { adminService } from '../services/adminService'

const AdminStats = () => {
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    loadStats()
  }, [])

  const loadStats = async () => {
    try {
      setLoading(true)
      const data = await adminService.getUsageStats()
      setStats(data)
    } catch (err) {
      setError('Failed to load statistics')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Total Users */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Total Users</h3>
        <p className="text-3xl font-bold text-gray-900">{stats?.totalUsers || 0}</p>
      </div>

      {/* Total Trades */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Total Trades</h3>
        <p className="text-3xl font-bold text-gray-900">{stats?.totalTrades || 0}</p>
      </div>

      {/* Subscription Stats */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Subscriptions</h3>
        <div className="space-y-3">
          {Object.entries(stats?.subscriptionStats || {}).map(([plan, data]) => (
            <div key={plan} className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700 capitalize">{plan}</span>
              <div className="flex space-x-4">
                <span className="text-sm text-green-600">Active: {data.active}</span>
                <span className="text-sm text-red-600">Inactive: {data.inactive}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default AdminStats