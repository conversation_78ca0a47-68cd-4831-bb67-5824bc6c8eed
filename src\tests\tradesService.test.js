import { tradesService, resetTrades } from '../services/tradesService'

// Mock the bhavcopyService
jest.mock('../services/bhavcopyService', () => ({
  bhavcopyService: {
    getCurrentPrice: jest.fn().mockResolvedValue(100),
    getHistoricalPrices: jest.fn().mockResolvedValue([])
  }
}))

describe('tradesService', () => {
  beforeEach(() => {
    // Clear any existing trades
    // In a real implementation, this would reset the Supabase data
    // For now, we'll just ensure a clean state
    resetTrades()
  })

  test('should add a new trade', async () => {
    const tradeData = {
      symbol: 'RELIANCE',
      tradeType: 'BUY',
      qty: '10',
      price: '2850',
      date: '2025-07-25',
      strategy: 'Momentum',
      note: 'Test trade'
    }

    const result = await tradesService.add(tradeData)
    
    expect(result).toBeDefined()
    expect(result.symbol).toBe('RELIANCE')
    expect(result.tradeType).toBe('BUY')
    expect(result.qty).toBe(10)
    expect(result.price).toBe(2850)
  })

  test('should get all trades', async () => {
    // First add a trade
    await tradesService.add({
      symbol: 'TATASTEEL',
      tradeType: 'SELL',
      qty: '5',
      price: '150',
      date: '2025-07-25'
    })

    const trades = await tradesService.getAll()
    
    expect(trades).toBeDefined()
    expect(trades.length).toBe(1)
    expect(trades[0].symbol).toBe('TATASTEEL')
  })

  test('should calculate portfolio summary', async () => {
    // Add some trades
    await tradesService.add({
      symbol: 'RELIANCE',
      tradeType: 'BUY',
      qty: '10',
      price: '2800',
      date: '2025-07-24'
    })

    await tradesService.add({
      symbol: 'RELIANCE',
      tradeType: 'SELL',
      qty: '5',
      price: '2850',
      date: '2025-07-25'
    })

    // Debug: Check the trades
    const trades = await tradesService.getAll()
    console.log('Trades:', trades)

    // Debug: Check the bhavcopyService mock
    const price = await require('../services/bhavcopyService').bhavcopyService.getCurrentPrice('RELIANCE')
    console.log('Current price:', price)

    const summary = await tradesService.getPortfolioSummary()
    
    expect(summary).toBeDefined()
    expect(summary.totalTrades).toBe(2)
    expect(summary.openPositions).toBe(1)
    expect(summary.realizedPnL).toBeGreaterThan(0)
    expect(summary.totalValue).toBeGreaterThan(0)
  })
})