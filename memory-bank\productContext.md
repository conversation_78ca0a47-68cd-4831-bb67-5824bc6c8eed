# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-07-25 18:15:00 - Memory Bank initialized.

*

## Project Goal

To offer a cloud-based platform where individual users can simulate stock trades using Bhavcopy data from BSE/NSE, validate strategies, and track virtual portfolios — with free and paid subscription tiers.

## Key Features

1. User Authentication
   - Email/password login via Supabase Auth
   - OAuth login support (Google, GitHub - optional)
   - User roles: free_user, pro_user, admin, superadmin

2. Virtual Trading Engine
   - Add manual buy/sell trades
   - Auto track portfolio performance with daily Bhavcopy updates

3. Strategy Tagging & Analytics
   - Tag each trade with a strategy
   - View per-strategy performance breakdown

4. Bhavcopy Importer
   - Auto Import Daily (can be setup as a CRON)
   - Parse and ingest NSE/BSE Bhavcopy CSV daily
   - Update prices in portfolio automatically

5. Subscription Tiers (Free vs Paid)
   - Free: 3 max trades, 1 strategy, no reports/backtest
   - Pro: 50 trades, 50 strategies, CSV export, historical test

6. Stripe Billing Integration
   - Checkout session creation via Supabase Edge Function
   - Webhooks for subscription status updates
   - User's plan tracked in user_subscriptions table

7. Portfolio & Strategy Reports
   - Track open positions, realized P/L, equity curve
   - Filter by strategy, time range
   - Exportable to CSV

8. Admin Panel (Basic)
   - View all users
   - Manually upgrade or suspend users
   - Monitor usage stats

## Overall Architecture

Frontend: React + Vite + Tailwind
Backend: Supabase (DB, Auth, Edge Functions)
Stock Charts: Lightweight-Charts by TradingView
Information Charts: Recharts / Chart.js
Payments: RazorPay
Hosting: Netlify + Supabase