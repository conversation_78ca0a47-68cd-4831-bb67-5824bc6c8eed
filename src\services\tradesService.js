// Mock data for initial development (will be removed when Supabase is fully implemented)
let trades = []

import { supabase } from './supabaseClient'
import { bhavcopyService } from './bhavcopyService'

// For testing purposes only
export const resetTrades = () => {
  trades = []
}

export const tradesService = {
  // Get all trades for the current user
  getAll: async () => {
    // For testing purposes, use in-memory array
    if (process.env.NODE_ENV === 'test') {
      return [...trades].sort((a, b) => new Date(a.date) - new Date(b.date))
    }

    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('No user logged in')

      const { data, error } = await supabase
        .from('virtual_trades')
        .select('*')
        .eq('user_id', user.id)
        .order('date', { ascending: false })

      if (error) throw error

      return data
    } catch (error) {
      console.error('Error fetching trades:', error)
      throw error
    }
  },

  // Add a new trade
  add: async (tradeData) => {
    // For testing purposes, use in-memory array
    if (process.env.NODE_ENV === 'test') {
      const newTrade = {
        id: Date.now(),
        ...tradeData,
        qty: Number(tradeData.qty),
        price: Number(tradeData.price)
      }
      trades.unshift(newTrade)
      return newTrade
    }

    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('No user logged in')

      const newTrade = {
        ...tradeData,
        qty: Number(tradeData.qty),
        price: Number(tradeData.price),
        user_id: user.id
      }

      const { data, error } = await supabase
        .from('virtual_trades')
        .insert([newTrade])
        .select()

      if (error) throw error

      return data[0]
    } catch (error) {
      console.error('Error adding trade:', error)
      throw error
    }
  },

  // Update a trade
  update: async (id, tradeData) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('No user logged in')

      const { data, error } = await supabase
        .from('virtual_trades')
        .update(tradeData)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()

      if (error) throw error

      return data[0]
    } catch (error) {
      console.error('Error updating trade:', error)
      throw error
    }
  },

  // Delete a trade
  delete: async (id) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('No user logged in')

      const { error } = await supabase
        .from('virtual_trades')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id)

      if (error) throw error

      return
    } catch (error) {
      console.error('Error deleting trade:', error)
      throw error
    }
  },

  // Get portfolio summary
  getPortfolioSummary: async () => {
    try {
      const trades = await tradesService.getAll()
      
      const summary = {
        totalTrades: trades.length,
        openPositions: 0,
        realizedPnL: 0,
        unrealizedPnL: 0,
        totalValue: 0,
        winRate: 0
      }

      // Calculate open positions and realized PnL
      const positions = {}
      let winningTrades = 0
      let totalClosedTrades = 0

      trades.forEach(trade => {
        if (!positions[trade.symbol]) {
          positions[trade.symbol] = { qty: 0, avgPrice: 0, cost: 0 }
        }

        if (trade.tradeType === 'BUY') {
          positions[trade.symbol].qty += trade.qty
          positions[trade.symbol].cost += trade.qty * trade.price
        } else if (trade.tradeType === 'SELL') {
          positions[trade.symbol].qty -= trade.qty
          positions[trade.symbol].cost -= trade.qty * trade.price
          
          // Calculate realized PnL for this sell
          const avgCost = positions[trade.symbol].cost / positions[trade.symbol].qty
          const realized = trade.qty * (trade.price - avgCost)
          summary.realizedPnL += realized
          
          totalClosedTrades++
          if (realized > 0) winningTrades++
        }
      })

      // Calculate open positions and unrealized PnL
      for (const symbol of Object.keys(positions)) {
        if (positions[symbol].qty > 0) {
          summary.openPositions++
          // Get current price from bhavcopy service
          try {
            const currentPrice = await bhavcopyService.getCurrentPrice(symbol)
            const currentValue = positions[symbol].qty * currentPrice
            const cost = positions[symbol].cost
            summary.unrealizedPnL += (currentValue - cost)
            summary.totalValue += currentValue
          } catch (err) {
            console.error(`Failed to get current price for ${symbol}`, err)
            // Use a fallback price if service fails
            const currentPrice = 100
            const currentValue = positions[symbol].qty * currentPrice
            const cost = positions[symbol].cost
            summary.unrealizedPnL += (currentValue - cost)
            summary.totalValue += currentValue
          }
        }
      }

      // Calculate win rate
      summary.winRate = totalClosedTrades > 0 ? (winningTrades / totalClosedTrades) * 100 : 0

      return summary
    } catch (error) {
      console.error('Error calculating portfolio summary:', error)
      throw error
    }
  },

  // Get trades by strategy
  getByStrategy: async (strategy) => {
    try {
      const trades = await tradesService.getAll()
      return trades.filter(t => t.strategy === strategy)
    } catch (error) {
      console.error('Error getting trades by strategy:', error)
      throw error
    }
  },

  // Get all strategies used
  getStrategies: async () => {
    try {
      const trades = await tradesService.getAll()
      const strategies = [...new Set(trades.map(t => t.strategy).filter(s => s))]
      return strategies
    } catch (error) {
      console.error('Error getting strategies:', error)
      throw error
    }
  },

  // Get strategy performance
  getStrategyPerformance: async (strategy) => {
    try {
      const strategyTrades = await tradesService.getByStrategy(strategy)
      if (strategyTrades.length === 0) {
        return {
          strategy,
          totalTrades: 0,
          winningTrades: 0,
          winRate: 0,
          totalPnL: 0,
          avgPnL: 0
        }
      }

      let winningTrades = 0
      let totalPnL = 0

      // Group trades by symbol to calculate PnL
      const positions = {}
      strategyTrades.forEach(trade => {
        if (!positions[trade.symbol]) {
          positions[trade.symbol] = { qty: 0, cost: 0 }
        }

        if (trade.tradeType === 'BUY') {
          positions[trade.symbol].qty += trade.qty
          positions[trade.symbol].cost += trade.qty * trade.price
        } else if (trade.tradeType === 'SELL') {
          positions[trade.symbol].qty -= trade.qty
          positions[trade.symbol].cost -= trade.qty * trade.price
          
          // Calculate PnL for this sell
          const avgCost = positions[trade.symbol].cost / positions[trade.symbol].qty
          const pnl = trade.qty * (trade.price - avgCost)
          totalPnL += pnl
          
          if (pnl > 0) winningTrades++
        }
      })

      const winRate = (winningTrades / strategyTrades.length) * 100
      const avgPnL = totalPnL / strategyTrades.length

      return {
        strategy,
        totalTrades: strategyTrades.length,
        winningTrades,
        winRate,
        totalPnL,
        avgPnL
      }
    } catch (error) {
      console.error('Error calculating strategy performance:', error)
      throw error
    }
  }
}