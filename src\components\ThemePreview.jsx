import React from 'react';

const colors = [
  { name: 'Primary (Dark Blue)', value: 'primary', hex: '#1e3a8a' },
  { name: 'Secondary (Yellow-Orange)', value: 'secondary', hex: '#f69e0b' },
  { name: 'Background', value: 'background', hex: '#ffffff' },
  { name: 'Heading (Soft Black)', value: 'heading', hex: '#222222' },
  { name: 'Text (Lighter Black)', value: 'text', hex: '#333333' },
  { name: 'Placeholder (Soft Grey)', value: 'placeholder', hex: '#888888' },
  { name: 'Border', value: 'border', hex: '#e5e5e5' },
  { name: 'Smoke (Faint Grey)', value: 'smoke', hex: '#f5f5f5' },
  { name: 'Error (Red)', value: 'error', hex: '#D10000' },
  { name: 'Success (Green)', value: 'success', hex: '#009900' },
];

export default function ThemePreview() {
  return (
    <div className="min-h-screen bg-background flex flex-col items-center justify-center p-8">
      <h1 className="text-3xl font-bold mb-8 text-heading">Theme Color Palette Preview</h1>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 w-full max-w-3xl">
        {colors.map((color) => (
          <div key={color.value} className="flex items-center space-x-4 p-4 rounded-lg shadow bg-smoke border border-border">
            <div
              className="w-16 h-16 rounded-full border-4 border-border shadow-md"
              style={{ backgroundColor: color.hex }}
            />
            <div>
              <div className="font-semibold text-heading">{color.name}</div>
              <div className="text-sm text-placeholder">tw: <span className="font-mono">bg-{color.value}</span></div>
              <div className="text-xs text-placeholder">{color.hex}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 