const LandingPage = ({ onEnterApp }) => {
  return (
    <div className="min-h-screen bg-gray-900 relative overflow-hidden">
      {/* Background Stock Chart Pattern */}
      <div 
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 50 Q 30 20 50 40 T 90 30' stroke='%2300ff88' stroke-width='2' fill='none'/%3E%3Cpath d='M10 70 Q 30 40 50 60 T 90 50' stroke='%23ff4444' stroke-width='2' fill='none'/%3E%3C/svg%3E")`,
          backgroundSize: '200px 200px',
          backgroundRepeat: 'repeat'
        }}
      />
      
      {/* Header */}
      <header className="relative z-10 px-6 py-4">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          {/* Logo */}
          <div className="text-2xl font-bold text-white">
            Virtual Stock Tester
          </div>
          
          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#features" className="text-gray-300 hover:text-white transition-colors">
              Features
            </a>
            <a href="#pricing" className="text-gray-300 hover:text-white transition-colors">
              Use Cases
            </a>
            <a href="#trial" className="text-gray-300 hover:text-white transition-colors">
              Free Trial
            </a>
            <button className="text-gray-300 hover:text-white transition-colors">
              Login
            </button>
            <button 
              onClick={onEnterApp}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              Start Free Trial
            </button>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <div className="relative z-10 flex items-center justify-center min-h-[80vh] px-6">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Master Your Trading Strategy
          </h1>
          <h2 className="text-3xl md:text-4xl font-bold text-orange-400 mb-8">
            Before You Risk Real Money
          </h2>
          <p className="text-xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
            Test your trading strategies with historical market data, analyze performance 
            metrics, and optimize your approach with our professional-grade 
            backtesting platform.
          </p>
          
          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button 
              onClick={onEnterApp}
              className="bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors shadow-lg"
            >
              Start Free Trial
            </button>
            <button className="border-2 border-gray-400 text-gray-300 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-800 hover:text-white transition-colors">
              Explore Features
            </button>
          </div>
        </div>
      </div>

      {/* Animated Background Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 opacity-10">
        <div className="w-full h-full border-2 border-green-400 rounded-full animate-pulse"></div>
      </div>
      <div className="absolute bottom-20 right-10 w-24 h-24 opacity-10">
        <div className="w-full h-full border-2 border-red-400 rounded-full animate-pulse delay-1000"></div>
      </div>
    </div>
  )
}

export default LandingPage

