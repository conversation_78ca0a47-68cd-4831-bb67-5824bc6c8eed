import { supabase } from './supabaseClient'

export const authService = {
  // Sign up a new user
  signUp: async (email, password) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password
      })
      
      if (error) throw error
      
      return data
    } catch (error) {
      console.error('Sign up error:', error)
      throw error
    }
  },

  // Sign in an existing user
  signIn: async (email, password) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      
      if (error) throw error
      
      return data
    } catch (error) {
      console.error('Sign in error:', error)
      throw error
    }
  },

  // Sign out the current user
  signOut: async () => {
    try {
      const { error } = await supabase.auth.signOut()
      
      if (error) throw error
    } catch (error) {
      console.error('Sign out error:', error)
      throw error
    }
  },

  // Get the current user session
  getSession: async () => {
    try {
      const { data, error } = await supabase.auth.getSession()
      
      if (error) throw error
      
      return data.session
    } catch (error) {
      console.error('Get session error:', error)
      throw error
    }
  },

  // Listen for auth state changes
  onAuthStateChange: (callback) => {
    return supabase.auth.onAuthStateChange((event, session) => {
      callback(event, session)
    })
  },

  // Get the current user
  getCurrentUser: async () => {
    try {
      const { data, error } = await supabase.auth.getUser()
      
      if (error) throw error
      
      return data.user
    } catch (error) {
      console.error('Get user error:', error)
      throw error
    }
  }
}