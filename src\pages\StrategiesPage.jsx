import { useState, useEffect } from 'react'
import { tradesService } from '../services/tradesService'

const StrategiesPage = () => {
  const [strategies, setStrategies] = useState([])
  const [strategyPerformance, setStrategyPerformance] = useState({})
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  useEffect(() => {
    loadStrategies()
  }, [])

  const loadStrategies = async () => {
    try {
      setLoading(true)
      const strategyNames = await tradesService.getStrategies()
      // Convert to objects with id and name
      const strategyObjects = strategyNames.map((name, index) => ({
        id: index + 1,
        name,
        description: `Strategy for ${name}`
      }))
      setStrategies(strategyObjects)

      // Load performance for each strategy
      const performanceData = {}
      for (const strategy of strategyObjects) {
        try {
          const performance = await tradesService.getStrategyPerformance(strategy.name)
          performanceData[strategy.name] = performance
        } catch (err) {
          console.error(`Failed to load performance for ${strategy.name}`, err)
        }
      }
      setStrategyPerformance(performanceData)
    } catch (err) {
      setError('Failed to load strategies')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (formData.name.trim()) {
      setStrategies([
        {
          id: Date.now(),
          ...formData
        },
        ...strategies
      ])
      setFormData({
        name: '',
        description: ''
      })
      setError(null)
    } else {
      setError('Strategy name is required')
    }
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleDelete = (id) => {
    setStrategies(strategies.filter(s => s.id !== id))
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Create New Strategy</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Strategy Name
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows="3"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          {error && (
            <div className="text-sm text-red-600">
              {error}
            </div>
          )}
          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Create Strategy
          </button>
        </form>
      </div>

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Your Strategies</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Performance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {strategies.length === 0 ? (
                <tr>
                  <td colSpan="4" className="px-6 py-4 text-sm text-gray-500 text-center">
                    No strategies yet. Create your first strategy to get started.
                  </td>
                </tr>
              ) : (
                strategies.map((strategy) => {
                  const performance = strategyPerformance[strategy.name] || {}
                  return (
                    <tr key={strategy.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {strategy.name}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {strategy.description}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {performance.totalTrades ? (
                          <div className="space-y-1">
                            <div>Win Rate: {performance.winRate.toFixed(1)}%</div>
                            <div>Total P&L: ₹{performance.totalPnL.toFixed(2)}</div>
                            <div>Avg P&L: ₹{performance.avgPnL.toFixed(2)}</div>
                          </div>
                        ) : (
                          <div className="text-gray-400">No trades</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleDelete(strategy.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  )
                })
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default StrategiesPage