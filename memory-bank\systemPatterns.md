# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-07-25 18:15:00 - Memory Bank initialized.

*

## Coding Patterns

* React with functional components and hooks
* Vite for fast development and build tooling
* Tailwind CSS for styling
* Supabase for backend services (auth, database, edge functions)

## Architectural Patterns

* Frontend-Backend separation with Supabase
* Role-based access control (RBAC) for user permissions
* Multi-tier subscription model (Free vs Pro)
* Component-based UI architecture

## Testing Patterns

* Unit testing for React components
* Integration testing for Supabase functions
* End-to-end testing for critical user flows
2025-07-25 19:39:00 - Completed: Documented all system patterns for Virtual Stock Tester Platform