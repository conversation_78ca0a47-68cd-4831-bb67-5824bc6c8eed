import { supabase } from './supabaseClient'

export const adminService = {
  // Get all users
  getAllUsers: async () => {
    try {
      // In a real implementation, this would require admin privileges
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error

      return data
    } catch (error) {
      console.error('Error fetching users:', error)
      throw error
    }
  },

  // Get user subscriptions
  getUserSubscriptions: async () => {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          users (email)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      return data
    } catch (error) {
      console.error('Error fetching user subscriptions:', error)
      throw error
    }
  },

  // Update user role
  updateUserRole: async (userId, role) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ role })
        .eq('id', userId)

      if (error) throw error

      return { success: true }
    } catch (error) {
      console.error('Error updating user role:', error)
      throw error
    }
  },

  // Update user subscription
  updateUserSubscription: async (userId, plan, status) => {
    try {
      const { error } = await supabase
        .from('user_subscriptions')
        .upsert({
          user_id: userId,
          plan,
          status,
          start_date: new Date().toISOString(),
          end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        }, {
          onConflict: 'user_id'
        })

      if (error) throw error

      return { success: true }
    } catch (error) {
      console.error('Error updating user subscription:', error)
      throw error
    }
  },

  // Get usage statistics
  getUsageStats: async () => {
    try {
      // Get total users
      const { count: totalUsers, error: usersError } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })

      if (usersError) throw usersError

      // Get total trades
      const { count: totalTrades, error: tradesError } = await supabase
        .from('virtual_trades')
        .select('*', { count: 'exact', head: true })

      if (tradesError) throw tradesError

      // Get subscription breakdown
      const { data: subscriptions, error: subsError } = await supabase
        .from('user_subscriptions')
        .select('plan, status')

      if (subsError) throw subsError

      const subscriptionStats = subscriptions.reduce((acc, sub) => {
        const plan = sub.plan || 'free'
        acc[plan] = acc[plan] || { active: 0, inactive: 0 }
        acc[plan][sub.status === 'active' ? 'active' : 'inactive']++
        return acc
      }, {})

      return {
        totalUsers,
        totalTrades,
        subscriptionStats
      }
    } catch (error) {
      console.error('Error fetching usage stats:', error)
      throw error
    }
  }
}